<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>會考成績積分積點計算</title>
    <style>
        /* CSS 變數系統 */
        :root {
            /* 間距系統 */
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 12px;
            --spacing-lg: 16px;
            --spacing-xl: 20px;
            --spacing-xxl: 24px;
            
            /* 字體系統 */
            --font-size-sm: 14px;
            --font-size-base: 16px;
            --font-size-lg: 18px;
            --font-size-xl: 20px;
            --font-size-xxl: 24px;
            
            /* 觸控目標 */
            --touch-target-min: 44px;
            --touch-target-comfortable: 48px;
            
            /* 斷點系統 */
            --breakpoint-sm: 375px;
            --breakpoint-md: 480px;
            --breakpoint-lg: 768px;
            --breakpoint-xl: 1024px;
            
            /* 顏色系統 */
            --color-primary: #3498db;
            --color-primary-dark: #2c3e50;
            --color-success: #27ae60;
            --color-success-light: #2ecc71;
            --color-danger: #e74c3c;
            --color-danger-dark: #c0392b;
            --color-text: #34495e;
            --color-text-light: #7f8c8d;
            --color-bg-light: #f8f9ff;
            --color-border: #ddd;
            --color-shadow: rgba(0, 0, 0, 0.1);
        }
        
        /* 基礎重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        /* 行動優先基礎樣式 */
        body {
            font-family: 'Microsoft JhengHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: var(--spacing-md); /* 減少最小螢幕的邊距 */
            font-size: var(--font-size-base);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            scroll-behavior: smooth;
        }
        
        /* 響應式容器 */
        .container {
            max-width: 100%;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: var(--spacing-lg); /* 小螢幕較小圓角 */
            box-shadow: 0 var(--spacing-lg) 30px var(--color-shadow);
            overflow: hidden;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }
        
        /* 媒體查詢斷點系統 */
        @media (min-width: 480px) {
            .container {
                max-width: 600px;
                border-radius: var(--spacing-xl);
                box-shadow: 0 var(--spacing-xl) 40px var(--color-shadow);
            }
            
            body {
                padding: var(--spacing-lg);
            }
        }
        
        @media (min-width: 768px) {
            .container {
                max-width: 700px;
            }
        }
        
        @media (min-width: 1024px) {
            .container {
                max-width: 800px;
            }
        }
        
        /* 標題區域樣式 */
        .header {
            background: linear-gradient(135deg, var(--color-primary-dark), var(--color-primary));
            color: white;
            padding: var(--spacing-xl) var(--spacing-lg);
            text-align: center;
        }
        
        .header h1 {
            font-size: clamp(1.5rem, 5vw, 2.2rem);
            margin-bottom: var(--spacing-sm);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            line-height: 1.2;
            font-weight: 700;
        }
        
        .header p {
            font-size: clamp(0.9rem, 3vw, 1.1rem);
            opacity: 0.9;
            margin: 0;
        }
        
        /* 響應式標題調整 */
        @media (min-width: 480px) {
            .header {
                padding: var(--spacing-xxl) var(--spacing-xl);
            }
        }
        
        @media (min-width: 768px) {
            .header {
                padding: 30px;
            }
        }
        
        /* 計算器主要區域 */
        .calculator-section {
            padding: var(--spacing-lg);
            position: relative;
        }
        
        /* 區段分隔線 */
        .calculator-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: var(--spacing-lg);
            right: var(--spacing-lg);
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--color-border), transparent);
        }
        
        @media (min-width: 480px) {
            .calculator-section {
                padding: var(--spacing-xl);
            }
            
            .calculator-section::before {
                left: var(--spacing-xl);
                right: var(--spacing-xl);
            }
        }
        
        @media (min-width: 768px) {
            .calculator-section {
                padding: 30px;
            }
            
            .calculator-section::before {
                left: 30px;
                right: 30px;
            }
        }
        
        /* 輸入群組 */
        .input-group {
            margin-bottom: var(--spacing-xl);
        }
        
        .input-group label {
            display: block;
            font-weight: bold;
            color: var(--color-text);
            margin-bottom: var(--spacing-lg);
            font-size: var(--font-size-lg);
            text-align: center;
        }
        
        /* 響應式成績網格 - 行動優先 */
        .exam-grades {
            display: grid;
            grid-template-columns: 1fr; /* 行動裝置單欄 */
            gap: var(--spacing-md);
            margin-top: var(--spacing-sm);
        }
        
        /* 小型手機以上 - 2欄 */
        @media (min-width: 375px) {
            .exam-grades {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        /* 標準手機以上 - 保持2欄但增加間距 */
        @media (min-width: 480px) {
            .exam-grades {
                gap: var(--spacing-lg);
            }
        }
        
        /* 平板以上 - 3欄 */
        @media (min-width: 768px) {
            .exam-grades {
                grid-template-columns: repeat(3, 1fr);
                gap: var(--spacing-lg);
            }
        }
        
        /* 成績輸入卡片 */
        .grade-input {
            text-align: center;
            background: var(--color-bg-light);
            padding: var(--spacing-md); /* 行動裝置較小內距 */
            border-radius: var(--spacing-sm);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease-out;
            border: 1px solid rgba(255, 255, 255, 0.8);
            position: relative;
            min-height: 80px; /* 確保一致高度 */
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .grade-input:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }
        
        @media (min-width: 480px) {
            .grade-input {
                padding: var(--spacing-lg);
                min-height: 90px;
            }
        }
        
        .grade-input label {
            font-size: var(--font-size-base);
            color: var(--color-text);
            margin-bottom: var(--spacing-sm);
            font-weight: bold;
            text-align: center;
        }
        
        /* 觸控友善的下拉選單 - 跨瀏覽器相容 */
        .grade-input select {
            width: 100%;
            height: var(--touch-target-comfortable); /* 48px 觸控友善高度 */
            padding: var(--spacing-md);
            border: 2px solid var(--color-border);
            border-radius: var(--spacing-sm);
            font-size: var(--font-size-base); /* 16px 防止iOS縮放 */
            font-family: inherit;
            background: white;
            color: var(--color-text);
            transition: all 0.2s ease-out;
            cursor: pointer;
            
            /* 跨瀏覽器外觀重置 */
            -webkit-appearance: none;
            -moz-appearance: none;
            -ms-appearance: none;
            appearance: none;
            
            /* 自定義下拉箭頭 - 跨瀏覽器 */
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right var(--spacing-md) center;
            background-size: 16px;
            padding-right: 40px;
            
            /* IE11 支援 */
            -ms-overflow-style: none;
        }
        
        /* IE11 特定修正 */
        .grade-input select::-ms-expand {
            display: none;
        }
        
        /* Firefox 特定修正 */
        @-moz-document url-prefix() {
            .grade-input select {
                padding-right: 30px;
                background-position: right 8px center;
            }
        }
        
        .grade-input select:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
            transform: translateY(-1px);
        }
        
        .grade-input select:hover {
            border-color: var(--color-primary);
        }
        
        /* 觸控狀態 */
        .grade-input select:active,
        .grade-input select.touch-active {
            transform: scale(0.98);
            background-color: #f0f8ff;
        }
        
        /* 選項樣式 */
        .grade-input select option {
            padding: var(--spacing-sm);
            font-size: var(--font-size-base);
        }
        
        /* 增強觸控回饋 */
        @media (hover: none) and (pointer: coarse) {
            .grade-input select {
                min-height: var(--touch-target-comfortable);
                font-size: 16px; /* 確保在觸控裝置上不縮放 */
            }
        }
        
        /* 結果顯示區域 - 行動優先 */
        .result-container {
            display: grid;
            grid-template-columns: 1fr; /* 行動裝置垂直堆疊 */
            gap: var(--spacing-lg);
            margin-top: var(--spacing-xl);
        }
        
        /* 標準手機以上 - 2欄並排 */
        @media (min-width: 480px) {
            .result-container {
                grid-template-columns: 1fr 1fr;
                gap: var(--spacing-xl);
            }
        }
        
        /* 結果框 */
        .result-box {
            color: white;
            padding: var(--spacing-xl);
            border-radius: var(--spacing-lg);
            text-align: center;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease-out;
            position: relative;
            overflow: hidden;
        }
        
        .result-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            pointer-events: none;
        }
        
        .result-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.2);
        }
        
        /* 積分框 */
        .score-box {
            background: linear-gradient(135deg, var(--color-success), var(--color-success-light));
        }
        
        /* 積點框 */
        .points-box {
            background: linear-gradient(135deg, var(--color-danger), var(--color-danger-dark));
        }
        
        /* 結果標題 */
        .result-title {
            font-size: clamp(1rem, 3vw, 1.2rem);
            margin-bottom: var(--spacing-sm);
            opacity: 0.9;
            font-weight: 600;
        }
        
        /* 響應式結果數值 */
        .result-value {
            font-size: clamp(2rem, 8vw, 3rem);
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            line-height: 1;
            margin: 0;
        }
        
        /* 平板以上的結果調整 */
        @media (min-width: 768px) {
            .result-box {
                padding: 25px;
            }
            
            .result-value {
                font-size: 3rem;
            }
        }
        
        /* 滾動優化 - 跨瀏覽器 */
        * {
            -webkit-overflow-scrolling: touch; /* iOS Safari */
            -ms-overflow-style: -ms-autohiding-scrollbar; /* IE/Edge */
        }
        
        /* Safari 特定修正 */
        @supports (-webkit-touch-callout: none) {
            .container {
                -webkit-transform: translate3d(0, 0, 0); /* 硬體加速 */
                transform: translate3d(0, 0, 0);
            }
            
            .grade-input select {
                -webkit-transform: translateZ(0); /* 修正渲染問題 */
                transform: translateZ(0);
            }
        }
        
        /* Chrome/Edge 特定修正 */
        @supports (display: grid) {
            .exam-grades {
                display: grid; /* 確保 Grid 支援 */
            }
        }
        
        /* 不支援 Grid 的瀏覽器回退 */
        @supports not (display: grid) {
            .exam-grades {
                display: flex;
                flex-wrap: wrap;
            }
            
            .grade-input {
                flex: 1 1 calc(50% - var(--spacing-md));
                margin: var(--spacing-sm);
            }
            
            @media (max-width: 374px) {
                .grade-input {
                    flex: 1 1 100%;
                }
            }
        }
        
        /* 不支援 CSS 變數的瀏覽器回退 */
        @supports not (--css: variables) {
            body {
                padding: 16px;
                font-size: 16px;
            }
            
            .calculator-section {
                padding: 16px;
            }
            
            .grade-input {
                padding: 12px;
            }
            
            .grade-input select {
                height: 48px;
                padding: 12px;
                font-size: 16px;
            }
        }
        
        /* 焦點可見性改善 */
        *:focus {
            outline: 2px solid var(--color-primary);
            outline-offset: 2px;
        }
        
        /* 選擇狀態優化 */
        ::selection {
            background-color: var(--color-primary);
            color: white;
        }
        
        ::-moz-selection {
            background-color: var(--color-primary);
            color: white;
        }
        
        /* 防止文字選擇在不需要的地方 */
        .result-box,
        .grade-input label {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        
        /* 改善視覺層次的額外間距 */
        .input-group + .result-container {
            margin-top: var(--spacing-xxl);
            padding-top: var(--spacing-lg);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        /* 無障礙輔助類別 */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
        
        /* 高對比模式支援 */
        @media (prefers-contrast: high) {
            .container {
                background: white;
                border: 2px solid black;
            }
            
            .header {
                background: black;
                color: white;
            }
            
            .grade-input select {
                border: 2px solid black;
                background: white;
            }
            
            .result-box {
                border: 2px solid black;
            }
        }
        
        /* 減少動畫偏好支援 - 完整實作 */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
                scroll-behavior: auto !important;
                transform: none !important;
            }
            
            /* 保持必要的功能性動畫 */
            .grade-input select:focus {
                transition: border-color 0.01ms, box-shadow 0.01ms;
            }
            
            .result-box:hover {
                transform: none;
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            }
            
            /* 移除裝飾性效果 */
            .result-box::before {
                display: none;
            }
            
            /* 簡化載入狀態 */
            body {
                opacity: 1 !important;
                transition: none !important;
            }
        }
        
        /* 動畫偏好檢測的 JavaScript 支援類別 */
        .reduce-motion * {
            animation: none !important;
            transition: none !important;
        }
        
        .reduce-motion .grade-input:hover {
            transform: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .reduce-motion .result-box:hover {
            transform: none;
        }
        
        /* 鍵盤導航增強 */
        select:focus-visible {
            outline: 3px solid var(--color-primary);
            outline-offset: 2px;
        }
        
        /* 確保足夠的色彩對比 */
        .result-title {
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }
        
        .result-value {
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header" role="banner">
            <h1>📊 會考積分積點計算器</h1>
            <p>輸入會考成績，立即計算積分與積點</p>
        </header>
        
        <main class="calculator-section" role="main" aria-label="會考成績計算器">
            <section class="input-group" aria-labelledby="grade-input-heading">
                <h2 id="grade-input-heading" class="sr-only">會考成績輸入</h2>
                <label aria-hidden="true">📝 會考成績輸入</label>
                <div class="exam-grades" role="group" aria-labelledby="grade-input-heading">
                    <div class="grade-input">
                        <label for="chinese">國文</label>
                        <select id="chinese" aria-describedby="chinese-desc" aria-label="選擇國文成績等級">
                            <option value="7">A++ (6分21點)</option>
                            <option value="6">A+ (5分18點)</option>
                            <option value="5" selected>A (4分15點)</option>
                            <option value="4">B++ (3分12點)</option>
                            <option value="3">B+ (2分9點)</option>
                            <option value="2">B (1分6點)</option>
                            <option value="1">C (0分3點)</option>
                        </select>
                        <div id="chinese-desc" class="sr-only">國文科目成績選擇，影響總積分和積點計算</div>
                    </div>
                    <div class="grade-input">
                        <label for="math">數學</label>
                        <select id="math" aria-describedby="math-desc" aria-label="選擇數學成績等級">
                            <option value="7">A++ (6分21點)</option>
                            <option value="6" selected>A+ (5分18點)</option>
                            <option value="5">A (4分15點)</option>
                            <option value="4">B++ (3分12點)</option>
                            <option value="3">B+ (2分9點)</option>
                            <option value="2">B (1分6點)</option>
                            <option value="1">C (0分3點)</option>
                        </select>
                        <div id="math-desc" class="sr-only">數學科目成績選擇，影響總積分和積點計算</div>
                    </div>
                    <div class="grade-input">
                        <label for="english">英語</label>
                        <select id="english" aria-describedby="english-desc" aria-label="選擇英語成績等級">
                            <option value="7">A++ (6分21點)</option>
                            <option value="6">A+ (5分18點)</option>
                            <option value="5" selected>A (4分15點)</option>
                            <option value="4">B++ (3分12點)</option>
                            <option value="3">B+ (2分9點)</option>
                            <option value="2">B (1分6點)</option>
                            <option value="1">C (0分3點)</option>
                        </select>
                        <div id="english-desc" class="sr-only">英語科目成績選擇，影響總積分和積點計算</div>
                    </div>
                    <div class="grade-input">
                        <label for="social">社會</label>
                        <select id="social" aria-describedby="social-desc" aria-label="選擇社會成績等級">
                            <option value="7">A++ (6分21點)</option>
                            <option value="6">A+ (5分18點)</option>
                            <option value="5" selected>A (4分15點)</option>
                            <option value="4">B++ (3分12點)</option>
                            <option value="3">B+ (2分9點)</option>
                            <option value="2">B (1分6點)</option>
                            <option value="1">C (0分3點)</option>
                        </select>
                        <div id="social-desc" class="sr-only">社會科目成績選擇，影響總積分和積點計算</div>
                    </div>
                    <div class="grade-input">
                        <label for="science">自然</label>
                        <select id="science" aria-describedby="science-desc" aria-label="選擇自然成績等級">
                            <option value="7">A++ (6分21點)</option>
                            <option value="6">A+ (5分18點)</option>
                            <option value="5" selected>A (4分15點)</option>
                            <option value="4">B++ (3分12點)</option>
                            <option value="3">B+ (2分9點)</option>
                            <option value="2">B (1分6點)</option>
                            <option value="1">C (0分3點)</option>
                        </select>
                        <div id="science-desc" class="sr-only">自然科目成績選擇，影響總積分和積點計算</div>
                    </div>
                    <div class="grade-input">
                        <label for="writing">寫作測驗</label>
                        <select id="writing" aria-describedby="writing-desc" aria-label="選擇寫作測驗級分">
                            <option value="6">6級分 (6分6點)</option>
                            <option value="5">5級分 (5分5點)</option>
                            <option value="4" selected>4級分 (4分4點)</option>
                            <option value="3">3級分 (3分3點)</option>
                            <option value="2">2級分 (2分2點)</option>
                            <option value="1">1級分 (1分1點)</option>
                        </select>
                        <div id="writing-desc" class="sr-only">寫作測驗級分選擇，影響總積分和積點計算</div>
                    </div>
                </div>
            </section>
            
            <section class="result-container" aria-label="計算結果" aria-live="polite" aria-atomic="true">
                <div class="result-box score-box" role="status" aria-labelledby="score-title">
                    <h3 id="score-title" class="result-title">總積分</h3>
                    <div class="result-value" id="total-score" aria-label="總積分數值">25</div>
                </div>
                <div class="result-box points-box" role="status" aria-labelledby="points-title">
                    <h3 id="points-title" class="result-title">總積點</h3>
                    <div class="result-value" id="total-points" aria-label="總積點數值">82</div>
                </div>
            </section>
        </main>
    </div>
    
    <script>
        // 積分對應表
        const scoreMap = {
            7: { grade: 'A++', score: 6, points: 21 },
            6: { grade: 'A+', score: 5, points: 18 },
            5: { grade: 'A', score: 4, points: 15 },
            4: { grade: 'B++', score: 3, points: 12 },
            3: { grade: 'B+', score: 2, points: 9 },
            2: { grade: 'B', score: 1, points: 6 },
            1: { grade: 'C', score: 0, points: 3 }
        };
        
        const writingMap = {
            6: { grade: '6級分', score: 6, points: 6 },
            5: { grade: '5級分', score: 5, points: 5 },
            4: { grade: '4級分', score: 4, points: 4 },
            3: { grade: '3級分', score: 3, points: 3 },
            2: { grade: '2級分', score: 2, points: 2 },
            1: { grade: '1級分', score: 1, points: 1 }
        };
        
        // 瀏覽器檢測
        const browserDetection = {
            isIOS: /iPad|iPhone|iPod/.test(navigator.userAgent),
            isAndroid: /Android/.test(navigator.userAgent),
            isSafari: /^((?!chrome|android).)*safari/i.test(navigator.userAgent),
            isChrome: /Chrome/.test(navigator.userAgent),
            isFirefox: /Firefox/.test(navigator.userAgent),
            isIE: /MSIE|Trident/.test(navigator.userAgent),
            
            // 功能檢測
            supportsTouch: 'ontouchstart' in window,
            supportsGrid: CSS.supports('display', 'grid'),
            supportsCSSVariables: CSS.supports('color', 'var(--test)')
        };
        
        // 觸控互動處理器 - 跨瀏覽器相容
        const touchHandler = {
            // 防止雙擊縮放 - iOS Safari 特定
            preventZoom: function(element) {
                if (!browserDetection.isIOS) return;
                
                let lastTouchEnd = 0;
                element.addEventListener('touchend', function(event) {
                    const now = (new Date()).getTime();
                    if (now - lastTouchEnd <= 300) {
                        event.preventDefault();
                    }
                    lastTouchEnd = now;
                }, { passive: false });
            },
            
            // 添加觸控回饋 - 跨瀏覽器
            addTouchFeedback: function(element) {
                if (browserDetection.supportsTouch) {
                    element.addEventListener('touchstart', function(e) {
                        this.classList.add('touch-active');
                    }, { passive: true });
                    
                    element.addEventListener('touchend', function(e) {
                        setTimeout(() => {
                            this.classList.remove('touch-active');
                        }, 150);
                    }, { passive: true });
                    
                    element.addEventListener('touchcancel', function(e) {
                        this.classList.remove('touch-active');
                    }, { passive: true });
                } else {
                    // 非觸控裝置使用滑鼠事件
                    element.addEventListener('mousedown', function(e) {
                        this.classList.add('touch-active');
                    });
                    
                    element.addEventListener('mouseup', function(e) {
                        setTimeout(() => {
                            this.classList.remove('touch-active');
                        }, 150);
                    });
                }
            },
            
            // 初始化所有觸控增強 - 跨瀏覽器
            init: function() {
                // 為所有下拉選單添加觸控回饋
                const selects = document.querySelectorAll('select');
                selects.forEach(select => {
                    this.addTouchFeedback(select);
                    this.preventZoom(select);
                    
                    // Android Chrome 特定修正
                    if (browserDetection.isAndroid && browserDetection.isChrome) {
                        select.style.fontSize = '16px'; // 防止縮放
                    }
                });
                
                // 為結果框添加觸控回饋
                const resultBoxes = document.querySelectorAll('.result-box');
                resultBoxes.forEach(box => {
                    this.addTouchFeedback(box);
                });
                
                // 防止整個文檔的雙擊縮放
                this.preventZoom(document.body);
                
                // IE11 特定修正
                if (browserDetection.isIE) {
                    document.body.classList.add('ie11');
                }
            }
        };
        
        // 節流函數
        function throttle(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            }
        }
        
        // 計算函數
        function calculateScoreAndPoints() {
            const subjects = ['chinese', 'math', 'english', 'social', 'science'];
            let totalScore = 0;
            let totalPoints = 0;
            
            // 計算五科成績
            subjects.forEach(subject => {
                const value = parseInt(document.getElementById(subject).value);
                const data = scoreMap[value];
                totalScore += data.score;
                totalPoints += data.points;
            });
            
            // 計算寫作測驗
            const writingValue = parseInt(document.getElementById('writing').value);
            const writingData = writingMap[writingValue];
            totalScore += writingData.score;
            totalPoints += writingData.points;
            
            // 更新顯示
            document.getElementById('total-score').textContent = totalScore;
            document.getElementById('total-points').textContent = totalPoints;
        }
        
        // 節流版本的計算函數
        const throttledCalculate = throttle(calculateScoreAndPoints, 100);
        
        // 動畫偏好檢測
        const motionPreferences = {
            // 檢測使用者動畫偏好
            prefersReducedMotion: function() {
                return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
            },
            
            // 應用動畫偏好設定
            applyMotionPreferences: function() {
                if (this.prefersReducedMotion()) {
                    document.body.classList.add('reduce-motion');
                    
                    // 禁用所有動畫相關的 JavaScript
                    const animatedElements = document.querySelectorAll('.grade-input, .result-box');
                    animatedElements.forEach(el => {
                        el.style.transition = 'none';
                        el.style.animation = 'none';
                    });
                    
                    // 簡化觸控回饋
                    const touchActiveStyle = document.createElement('style');
                    touchActiveStyle.textContent = `
                        .reduce-motion .touch-active {
                            transform: none !important;
                            opacity: 0.9 !important;
                        }
                    `;
                    document.head.appendChild(touchActiveStyle);
                }
            },
            
            // 監聽動畫偏好變更
            watchMotionPreferences: function() {
                const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
                mediaQuery.addListener((e) => {
                    if (e.matches) {
                        document.body.classList.add('reduce-motion');
                    } else {
                        document.body.classList.remove('reduce-motion');
                    }
                });
            },
            
            // 初始化動畫偏好處理
            init: function() {
                this.applyMotionPreferences();
                this.watchMotionPreferences();
            }
        };
        
        // 效能優化器
        const performanceOptimizer = {
            // 預載入關鍵資源
            preloadResources: function() {
                // 預載入字體
                const fontLink = document.createElement('link');
                fontLink.rel = 'preload';
                fontLink.as = 'font';
                fontLink.type = 'font/woff2';
                fontLink.crossOrigin = 'anonymous';
                document.head.appendChild(fontLink);
            },
            
            // 延遲載入非關鍵資源
            lazyLoadNonCritical: function() {
                // 延遲載入裝飾性元素
                setTimeout(() => {
                    document.body.classList.add('loaded');
                }, 100);
            },
            
            // 優化重繪
            optimizeRepaints: function() {
                // 使用 requestAnimationFrame 優化動畫
                const elements = document.querySelectorAll('.result-value');
                elements.forEach(el => {
                    el.style.willChange = 'contents';
                });
            },
            
            // 記憶體清理
            cleanup: function() {
                // 清理不需要的事件監聽器
                window.addEventListener('beforeunload', function() {
                    // 清理操作
                });
            },
            
            // 初始化所有優化
            init: function() {
                this.preloadResources();
                this.lazyLoadNonCritical();
                this.optimizeRepaints();
                this.cleanup();
                
                // 初始化動畫偏好
                motionPreferences.init();
            }
        };
        
        // 載入狀態管理
        const loadingManager = {
            showLoading: function() {
                // 可以添加載入指示器
                document.body.style.opacity = '0.9';
            },
            
            hideLoading: function() {
                document.body.style.opacity = '1';
                document.body.style.transition = 'opacity 0.3s ease-out';
            }
        };
        
        // 優化的計算函數（添加快取）
        let calculationCache = new Map();
        
        function optimizedCalculateScoreAndPoints() {
            const subjects = ['chinese', 'math', 'english', 'social', 'science'];
            const writing = 'writing';
            
            // 創建快取鍵
            const cacheKey = subjects.map(s => document.getElementById(s).value).join('-') + 
                           '-' + document.getElementById(writing).value;
            
            // 檢查快取
            if (calculationCache.has(cacheKey)) {
                const cached = calculationCache.get(cacheKey);
                document.getElementById('total-score').textContent = cached.score;
                document.getElementById('total-points').textContent = cached.points;
                return;
            }
            
            let totalScore = 0;
            let totalPoints = 0;
            
            // 計算五科成績
            subjects.forEach(subject => {
                const value = parseInt(document.getElementById(subject).value);
                const data = scoreMap[value];
                totalScore += data.score;
                totalPoints += data.points;
            });
            
            // 計算寫作測驗
            const writingValue = parseInt(document.getElementById(writing).value);
            const writingData = writingMap[writingValue];
            totalScore += writingData.score;
            totalPoints += writingData.points;
            
            // 快取結果
            calculationCache.set(cacheKey, { score: totalScore, points: totalPoints });
            
            // 限制快取大小
            if (calculationCache.size > 50) {
                const firstKey = calculationCache.keys().next().value;
                calculationCache.delete(firstKey);
            }
            
            // 使用 requestAnimationFrame 優化 DOM 更新
            requestAnimationFrame(() => {
                document.getElementById('total-score').textContent = totalScore;
                document.getElementById('total-points').textContent = totalPoints;
            });
        }
        
        // 節流版本的優化計算函數
        const throttledOptimizedCalculate = throttle(optimizedCalculateScoreAndPoints, 50);
        
        // 最終整合和測試
        const finalIntegration = {
            // 驗證所有功能
            validateFeatures: function() {
                console.log('🔍 驗證行動優化功能...');
                
                // 檢查響應式佈局
                const container = document.querySelector('.container');
                const examGrades = document.querySelector('.exam-grades');
                const resultContainer = document.querySelector('.result-container');
                
                if (container && examGrades && resultContainer) {
                    console.log('✅ 響應式佈局元素存在');
                } else {
                    console.error('❌ 響應式佈局元素缺失');
                }
                
                // 檢查觸控優化
                const selects = document.querySelectorAll('select');
                let touchOptimized = true;
                selects.forEach(select => {
                    const height = window.getComputedStyle(select).height;
                    if (parseInt(height) < 44) {
                        touchOptimized = false;
                    }
                });
                
                if (touchOptimized) {
                    console.log('✅ 觸控目標大小符合標準');
                } else {
                    console.warn('⚠️ 部分觸控目標可能過小');
                }
                
                // 檢查計算功能
                const totalScore = document.getElementById('total-score');
                const totalPoints = document.getElementById('total-points');
                
                if (totalScore && totalPoints && totalScore.textContent && totalPoints.textContent) {
                    console.log('✅ 計算功能正常');
                } else {
                    console.error('❌ 計算功能異常');
                }
                
                console.log('🎉 行動優化驗證完成！');
            },
            
            // 初始化完整功能
            init: function() {
                // 延遲驗證以確保所有元素已載入
                setTimeout(() => {
                    this.validateFeatures();
                }, 500);
            }
        };
        
        // 事件監聽器
        document.addEventListener('DOMContentLoaded', function() {
            loadingManager.showLoading();
            
            // 初始化效能優化
            performanceOptimizer.init();
            
            // 初始化觸控處理
            touchHandler.init();
            
            // 初始計算
            optimizedCalculateScoreAndPoints();
            
            // 監聽變更事件（使用事件委派）
            document.addEventListener('change', function(e) {
                if (e.target.tagName === 'SELECT') {
                    throttledOptimizedCalculate();
                }
            });
            
            // 初始化最終整合
            finalIntegration.init();
            
            loadingManager.hideLoading();
        });
        
        // 視窗載入完成後的處理
        window.addEventListener('load', function() {
            optimizedCalculateScoreAndPoints();
            
            // 標記頁面完全載入
            document.body.classList.add('fully-loaded');
            
            // 最終檢查
            console.log('📱 會考積分積點計算器 - 行動版已完全載入');
            console.log('🚀 所有行動優化功能已啟用');
        });
    </script>
</body>
</html>